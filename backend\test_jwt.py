#!/usr/bin/env python3
"""
JWT功能测试脚本
"""

try:
    import jwt
    import datetime
    
    print("🔍 测试JWT功能...")
    
    # 测试数据
    secret_key = "test-secret-key"
    payload = {
        'user_id': 1,
        'username': 'testuser',
        'exp': datetime.datetime.utcnow() + datetime.timedelta(hours=24)
    }
    
    print(f"JWT版本: {jwt.__version__}")
    
    # 测试编码
    try:
        token = jwt.encode(payload, secret_key, algorithm='HS256')
        print(f"✅ JWT编码成功")
        print(f"Token类型: {type(token)}")
        print(f"Token: {token}")
        
        # 确保是字符串
        if isinstance(token, bytes):
            token = token.decode('utf-8')
            print("🔧 转换为字符串格式")
        
        # 测试解码
        decoded = jwt.decode(token, secret_key, algorithms=['HS256'])
        print(f"✅ JWT解码成功: {decoded}")
        
    except Exception as e:
        print(f"❌ JWT操作失败: {e}")
        print(f"错误类型: {type(e)}")
        
except ImportError as e:
    print(f"❌ 导入JWT失败: {e}")
except Exception as e:
    print(f"❌ 未知错误: {e}")

@echo off
echo 🔐 安全登录系统启动脚本
echo ================================

echo.
echo 检查环境...

:: 检查Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到 Node.js，请先安装 Node.js
    pause
    exit /b 1
)

:: 检查Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到 Python，请先安装 Python
    pause
    exit /b 1
)

echo ✅ 环境检查通过

echo.
echo 安装依赖...

:: 安装前端依赖
echo 📦 安装前端依赖...
call npm install
if %errorlevel% neq 0 (
    echo ❌ 前端依赖安装失败
    pause
    exit /b 1
)

:: 安装后端依赖
echo 📦 安装后端依赖...
cd backend
call pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ 后端依赖安装失败
    pause
    exit /b 1
)
cd ..

echo ✅ 依赖安装完成

echo.
echo 🚀 启动服务...

:: 启动后端服务（后台运行）
echo 启动后端服务...
start "后端服务" cmd /k "cd backend && python conn.py"

:: 等待后端启动
timeout /t 3 /nobreak >nul

:: 启动前端服务
echo 启动前端服务...
start "前端服务" cmd /k "npm run dev"

echo.
echo 🎉 服务启动完成！
echo.
echo 📝 访问地址:
echo    前端: http://localhost:5173
echo    后端: http://localhost:5000
echo.
echo 💡 提示:
echo    - 首次运行需要配置数据库，请运行 backend/setup.py
echo    - 按 Ctrl+C 可以停止服务
echo.

pause

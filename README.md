# 🔐 安全登录系统

一个基于 Vue 3 + Flask 的安全登录注册系统，实现了现代Web应用的安全最佳实践。

## ✨ 特性

### 🛡️ 安全特性
- **密码哈希存储**: 使用 Werkzeug 的安全哈希算法
- **JWT 认证**: 无状态的用户认证机制
- **输入验证**: 前后端双重验证
- **SQL 注入防护**: 使用参数化查询
- **CORS 安全配置**: 限制跨域访问
- **环境变量管理**: 敏感信息不硬编码

### 🎨 用户体验
- **响应式设计**: 适配各种屏幕尺寸
- **实时验证**: 即时的表单验证反馈
- **加载状态**: 清晰的操作状态提示
- **错误处理**: 友好的错误信息显示
- **路由守卫**: 自动的登录状态管理

### 🔧 技术栈
- **前端**: Vue 3, Vue Router, Axios, Vite
- **后端**: Flask, MySQL, JWT, Werkzeug
- **安全**: bcrypt, CORS, 环境变量

## 🚀 快速开始

### 环境要求
- Node.js 16+
- Python 3.8+
- MySQL 5.7+

### 1. 克隆项目
```bash
git clone <repository-url>
cd security
```

### 2. 后端设置

#### 安装Python依赖
```bash
cd backend
pip install -r requirements.txt
```

#### 自动设置（推荐）
```bash
python setup.py
```
这个脚本会帮你：
- 安装所有依赖
- 创建 `.env` 配置文件
- 初始化数据库和表
- 创建测试用户（可选）

#### 手动设置
1. 复制环境变量模板：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，填入你的数据库配置：
```env
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=security
SECRET_KEY=your-secret-key
```

3. 初始化数据库：
```bash
mysql -u root -p < init_db.sql
```

#### 启动后端服务
```bash
python conn.py
```
服务将在 http://localhost:5000 启动

### 3. 前端设置

#### 安装依赖
```bash
npm install
# 或
yarn install
```

#### 启动开发服务器
```bash
npm run dev
# 或
yarn dev
```
前端将在 http://localhost:5173 启动

## 📖 使用说明

### 注册新用户
1. 访问 http://localhost:5173
2. 点击"立即注册"切换到注册模式
3. 填写用户名、邮箱和密码
4. 点击"立即注册"

### 登录
1. 在登录页面输入用户名/邮箱和密码
2. 点击"立即登录"
3. 成功后会跳转到主页

### 密码要求
- 至少8位字符
- 必须包含字母和数字
- 用户名：3-20位字母、数字或下划线

## 🔒 安全实现详解

### 密码安全
- 使用 Werkzeug 的 `generate_password_hash()` 进行密码哈希
- 密码永远不以明文形式存储或传输
- 实现了强密码策略

### 认证机制
- JWT token 用于维持登录状态
- Token 包含过期时间，默认24小时
- 自动的 token 验证和刷新

### 数据验证
- 前端实时验证用户输入
- 后端严格验证所有接收的数据
- 使用正则表达式验证邮箱和用户名格式

### 数据库安全
- 使用参数化查询防止 SQL 注入
- 数据库连接信息通过环境变量管理
- 用户表包含必要的索引优化查询性能

## 🛠️ API 接口

### 用户注册
```http
POST /api/register
Content-Type: application/json

{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123"
}
```

### 用户登录
```http
POST /api/login
Content-Type: application/json

{
  "login": "testuser",  // 用户名或邮箱
  "password": "password123"
}
```

### 获取用户信息
```http
GET /api/user/profile
Authorization: Bearer <jwt_token>
```

### 验证Token
```http
POST /api/verify-token
Authorization: Bearer <jwt_token>
```

## 🗄️ 数据库结构

### user 表
```sql
CREATE TABLE user (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);
```

## 🔧 配置选项

### 环境变量
| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `DB_HOST` | 数据库主机 | localhost |
| `DB_USER` | 数据库用户名 | root |
| `DB_PASSWORD` | 数据库密码 | - |
| `DB_NAME` | 数据库名称 | security |
| `SECRET_KEY` | JWT 密钥 | - |
| `FLASK_ENV` | Flask 环境 | development |
| `PORT` | 服务器端口 | 5000 |

## 🚨 安全注意事项

### 生产环境部署
1. **更改默认密钥**: 生成强随机的 `SECRET_KEY`
2. **使用 HTTPS**: 确保所有通信都经过加密
3. **数据库安全**: 使用强密码，限制数据库访问权限
4. **环境变量**: 不要将 `.env` 文件提交到版本控制
5. **CORS 配置**: 限制允许的域名
6. **日志监控**: 实现安全审计日志

### 已知限制
- 目前不支持密码重置功能
- 没有实现账户锁定机制
- 缺少详细的安全审计日志

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

## 📄 许可证

MIT License

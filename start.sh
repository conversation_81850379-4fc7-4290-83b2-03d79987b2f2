#!/bin/bash

echo "🔐 安全登录系统启动脚本"
echo "================================"

echo ""
echo "检查环境..."

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ 未找到 Node.js，请先安装 Node.js"
    exit 1
fi

# 检查Python
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo "❌ 未找到 Python，请先安装 Python"
    exit 1
fi

# 确定Python命令
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
else
    PYTHON_CMD="python"
fi

echo "✅ 环境检查通过"

echo ""
echo "安装依赖..."

# 安装前端依赖
echo "📦 安装前端依赖..."
npm install
if [ $? -ne 0 ]; then
    echo "❌ 前端依赖安装失败"
    exit 1
fi

# 安装后端依赖
echo "📦 安装后端依赖..."
cd backend
$PYTHON_CMD -m pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "❌ 后端依赖安装失败"
    exit 1
fi
cd ..

echo "✅ 依赖安装完成"

echo ""
echo "🚀 启动服务..."

# 启动后端服务（后台运行）
echo "启动后端服务..."
cd backend
$PYTHON_CMD conn.py &
BACKEND_PID=$!
cd ..

# 等待后端启动
sleep 3

# 启动前端服务
echo "启动前端服务..."
npm run dev &
FRONTEND_PID=$!

echo ""
echo "🎉 服务启动完成！"
echo ""
echo "📝 访问地址:"
echo "   前端: http://localhost:5173"
echo "   后端: http://localhost:5000"
echo ""
echo "💡 提示:"
echo "   - 首次运行需要配置数据库，请运行 backend/setup.py"
echo "   - 按 Ctrl+C 可以停止服务"
echo ""

# 等待用户中断
trap "echo ''; echo '停止服务...'; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit 0" INT

# 保持脚本运行
wait

# 📁 项目结构说明

```
security/
├── 📁 backend/                 # 后端Flask应用
│   ├── conn.py                 # 主应用文件，包含所有API端点
│   ├── setup.py                # 自动化设置脚本
│   ├── init_db.sql             # 数据库初始化SQL脚本
│   ├── requirements.txt        # Python依赖列表
│   ├── .env.example           # 环境变量模板
│   └── .env                   # 环境变量配置（需要创建）
│
├── 📁 src/                     # 前端Vue应用源码
│   ├── 📁 components/          # Vue组件
│   │   ├── Login.vue          # 登录注册组件
│   │   └── Home.vue           # 主页组件
│   ├── 📁 router/             # 路由配置
│   │   └── index.js           # 路由定义和守卫
│   ├── 📁 utils/              # 工具函数
│   │   └── api.js             # API请求和认证工具
│   ├── App.vue                # 根组件
│   ├── main.js                # 应用入口
│   └── style.css              # 全局样式
│
├── 📁 public/                  # 静态资源
├── 📄 package.json             # 前端依赖配置
├── 📄 vite.config.js          # Vite构建配置
├── 📄 index.html              # HTML模板
├── 📄 README.md               # 项目说明文档
├── 📄 PROJECT_STRUCTURE.md    # 项目结构说明（本文件）
├── 🚀 start.bat               # Windows启动脚本
└── 🚀 start.sh                # Linux/Mac启动脚本
```

## 🔧 核心文件说明

### 后端文件

#### `backend/conn.py`
- **作用**: Flask应用主文件
- **功能**: 
  - 用户注册API (`/api/register`)
  - 用户登录API (`/api/login`)
  - 用户信息API (`/api/user/profile`)
  - Token验证API (`/api/verify-token`)
  - JWT认证装饰器
  - 输入验证函数
  - 数据库连接管理

#### `backend/setup.py`
- **作用**: 自动化设置脚本
- **功能**:
  - 创建环境变量配置
  - 初始化数据库和表
  - 创建测试用户
  - 安装Python依赖

#### `backend/init_db.sql`
- **作用**: 数据库初始化脚本
- **功能**:
  - 创建`user`表
  - 创建`login_log`表（可选）
  - 设置索引和约束

### 前端文件

#### `src/components/Login.vue`
- **作用**: 登录注册页面组件
- **功能**:
  - 登录表单处理
  - 注册表单处理
  - 客户端输入验证
  - 错误和成功消息显示
  - 加载状态管理

#### `src/components/Home.vue`
- **作用**: 登录后的主页组件
- **功能**:
  - 显示用户信息
  - 退出登录功能
  - 认证状态检查

#### `src/router/index.js`
- **作用**: 路由配置文件
- **功能**:
  - 定义应用路由
  - 实现路由守卫
  - 自动重定向逻辑

#### `src/utils/api.js`
- **作用**: API请求和认证工具
- **功能**:
  - Axios实例配置
  - 请求/响应拦截器
  - 认证API封装
  - 认证状态管理工具

## 🔐 安全特性实现

### 密码安全
- **位置**: `backend/conn.py` - `generate_password_hash()`, `check_password_hash()`
- **实现**: 使用Werkzeug的安全哈希算法

### JWT认证
- **位置**: `backend/conn.py` - `@token_required` 装饰器
- **实现**: JWT token生成、验证和过期处理

### 输入验证
- **后端**: `backend/conn.py` - `validate_*()` 函数
- **前端**: `src/components/Login.vue` - 客户端验证函数

### SQL注入防护
- **位置**: `backend/conn.py` - 所有数据库查询
- **实现**: 使用参数化查询

### CORS安全
- **位置**: `backend/conn.py` - CORS配置
- **实现**: 限制允许的域名和方法

## 🚀 启动流程

### 自动启动（推荐）
```bash
# Windows
start.bat

# Linux/Mac
chmod +x start.sh
./start.sh
```

### 手动启动
1. **后端设置**:
   ```bash
   cd backend
   python setup.py  # 首次运行
   python conn.py   # 启动服务
   ```

2. **前端启动**:
   ```bash
   npm install      # 安装依赖
   npm run dev      # 启动开发服务器
   ```

## 📊 数据流

### 注册流程
```
用户输入 → 前端验证 → API请求 → 后端验证 → 密码哈希 → 数据库存储 → JWT生成 → 返回token
```

### 登录流程
```
用户输入 → 前端验证 → API请求 → 后端验证 → 密码验证 → JWT生成 → 返回token
```

### 认证流程
```
请求发送 → 拦截器添加token → 后端验证token → 返回数据/错误
```

## 🔧 配置说明

### 环境变量 (`.env`)
- `DB_*`: 数据库连接配置
- `SECRET_KEY`: JWT签名密钥
- `FLASK_ENV`: Flask运行环境
- `PORT`: 服务器端口

### 前端配置
- `vite.config.js`: Vite构建配置
- `package.json`: 依赖和脚本配置

### 后端配置
- `requirements.txt`: Python依赖
- CORS设置在 `conn.py` 中配置

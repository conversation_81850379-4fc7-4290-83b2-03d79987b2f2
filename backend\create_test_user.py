#!/usr/bin/env python3
"""
快速创建测试用户脚本
"""

import mysql.connector
from mysql.connector import Error
from werkzeug.security import generate_password_hash
from dotenv import load_dotenv
import os
import datetime

# 加载环境变量
load_dotenv()

# 数据库连接配置
db_config = {
    'host': os.environ.get('DB_HOST', 'localhost'),
    'user': os.environ.get('DB_USER', 'root'),
    'password': os.environ.get('DB_PASSWORD', '123456'),
    'database': os.environ.get('DB_NAME', 'security'),
    'autocommit': True
}

def create_test_user():
    """创建测试用户"""
    try:
        print("🔧 连接数据库...")
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        
        # 检查用户表是否存在
        cursor.execute("SHOW TABLES LIKE 'user'")
        if not cursor.fetchone():
            print("❌ user表不存在，请先运行数据库初始化脚本")
            return False
        
        # 检查测试用户是否已存在
        cursor.execute("SELECT id FROM user WHERE username = 'admin' OR email = '<EMAIL>'")
        if cursor.fetchone():
            print("⚠️  测试用户已存在")
            print("   用户名: admin")
            print("   邮箱: <EMAIL>") 
            print("   密码: admin123")
            return True
        
        # 创建测试用户
        print("🔧 创建测试用户...")
        password_hash = generate_password_hash('admin123')
        
        insert_query = """
            INSERT INTO user (username, email, password_hash, created_at) 
            VALUES (%s, %s, %s, %s)
        """
        cursor.execute(insert_query, ('admin', '<EMAIL>', password_hash, datetime.datetime.now()))
        
        print("✅ 测试用户创建成功!")
        print("   用户名: admin")
        print("   邮箱: <EMAIL>")
        print("   密码: admin123")
        
        return True
        
    except Error as e:
        print(f"❌ 创建用户失败: {e}")
        return False
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def create_database_and_table():
    """创建数据库和表"""
    try:
        print("🔧 创建数据库和表...")
        
        # 连接MySQL服务器（不指定数据库）
        connection = mysql.connector.connect(
            host=db_config['host'],
            user=db_config['user'],
            password=db_config['password']
        )
        cursor = connection.cursor()
        
        # 创建数据库
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS {db_config['database']} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print(f"✅ 数据库 '{db_config['database']}' 创建成功!")
        
        # 使用数据库
        cursor.execute(f"USE {db_config['database']}")
        
        # 创建用户表
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS user (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            email VARCHAR(100) NOT NULL UNIQUE,
            password_hash VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT TRUE,
            INDEX idx_username (username),
            INDEX idx_email (email)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """
        
        cursor.execute(create_table_sql)
        print("✅ user表创建成功!")
        
        return True
        
    except Error as e:
        print(f"❌ 创建数据库/表失败: {e}")
        return False
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def main():
    print("🔐 快速设置测试用户")
    print("=" * 30)
    
    # 先尝试创建测试用户
    if not create_test_user():
        print("\n💡 尝试创建数据库和表...")
        if create_database_and_table():
            print("\n🔧 重新创建测试用户...")
            create_test_user()
    
    print("\n🎉 设置完成!")
    print("\n📝 测试登录信息:")
    print("   用户名: admin")
    print("   邮箱: <EMAIL>")
    print("   密码: admin123")
    print("\n🚀 现在可以启动服务器并测试登录了!")

if __name__ == "__main__":
    main()

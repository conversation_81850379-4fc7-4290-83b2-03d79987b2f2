<template>
  <div class="home-container">
    <div class="header">
      <h1>欢迎来到安全系统</h1>
      <div class="user-info">
        <span v-if="user">欢迎，{{ user.username }}!</span>
        <button @click="logout" class="logout-btn">退出登录</button>
      </div>
    </div>
    
    <div class="content">
      <div class="card">
        <h2>用户信息</h2>
        <div v-if="user" class="user-details">
          <p><strong>用户名:</strong> {{ user.username }}</p>
          <p><strong>邮箱:</strong> {{ user.email }}</p>
          <p><strong>用户ID:</strong> {{ user.id }}</p>
        </div>
        <div v-else class="loading">
          加载用户信息中...
        </div>
      </div>
      
      <div class="card">
        <h2>系统功能</h2>
        <ul>
          <li>✅ 安全的用户认证</li>
          <li>✅ JWT Token管理</li>
          <li>✅ 密码哈希存储</li>
          <li>✅ 输入验证</li>
          <li>✅ SQL注入防护</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { authAPI, authUtils } from '../utils/api';

const router = useRouter();
const user = ref(null);

// 检查登录状态
const checkAuth = () => {
  if (!authUtils.isAuthenticated()) {
    router.push('/');
    return false;
  }

  user.value = authUtils.getCurrentUser();
  return true;
};

// 验证token有效性
const verifyToken = async () => {
  try {
    await authAPI.verifyToken();
  } catch (error) {
    console.error('Token验证失败:', error);
    logout();
  }
};

// 退出登录
const logout = () => {
  authUtils.clearAuth();
  router.push('/');
};

onMounted(() => {
  if (checkAuth()) {
    verifyToken();
  }
});
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 20px 30px;
  border-radius: 15px;
  margin-bottom: 30px;
  color: white;
}

.header h1 {
  margin: 0;
  font-size: 2.5rem;
  font-weight: 300;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.logout-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 10px 20px;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.card {
  background: rgba(255, 255, 255, 0.95);
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.card h2 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.8rem;
  font-weight: 500;
}

.user-details p {
  margin: 10px 0;
  font-size: 1.1rem;
  color: #555;
}

.user-details strong {
  color: #333;
}

.card ul {
  list-style: none;
  padding: 0;
}

.card li {
  padding: 10px 0;
  font-size: 1.1rem;
  color: #555;
  border-bottom: 1px solid #eee;
}

.card li:last-child {
  border-bottom: none;
}

.loading {
  text-align: center;
  color: #666;
  font-style: italic;
}
</style>

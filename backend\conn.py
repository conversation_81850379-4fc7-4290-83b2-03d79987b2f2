from flask import Flask, jsonify, request
import mysql.connector
from mysql.connector import Error
from flask_cors import CORS
from werkzeug.security import generate_password_hash, check_password_hash
import jwt
import datetime
import os
import re
from functools import wraps
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

app = Flask(__name__)

# 安全配置
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'your-secret-key-change-in-production')
app.config['JWT_EXPIRATION_DELTA'] = datetime.timedelta(hours=24)

# CORS配置 - 仅允许特定来源
CORS(app, origins=['http://localhost:3000', 'http://localhost:5173'],
     methods=['GET', 'POST', 'PUT', 'DELETE'],
     allow_headers=['Content-Type', 'Authorization'])

# 数据库连接配置 - 使用环境变量
db_config = {
    'host': os.environ.get('DB_HOST', 'localhost'),
    'user': os.environ.get('DB_USER', 'root'),
    'password': os.environ.get('DB_PASSWORD', '123456'),
    'database': os.environ.get('DB_NAME', 'security'),
    'autocommit': True
}

# 输入验证函数
def validate_email(email):
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_password(password):
    # 密码至少8位，包含字母和数字
    if len(password) < 8:
        return False
    if not re.search(r'[A-Za-z]', password):
        return False
    if not re.search(r'\d', password):
        return False
    return True

def validate_username(username):
    # 用户名3-20位，只能包含字母、数字和下划线
    pattern = r'^[a-zA-Z0-9_]{3,20}$'
    return re.match(pattern, username) is not None

# JWT装饰器
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'error': '缺少认证token'}), 401

        try:
            if token.startswith('Bearer '):
                token = token[7:]
            data = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
            current_user_id = data['user_id']
        except jwt.ExpiredSignatureError:
            return jsonify({'error': 'Token已过期'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'error': '无效的token'}), 401

        return f(current_user_id, *args, **kwargs)
    return decorated

# 数据库连接函数
def get_db_connection():
    try:
        connection = mysql.connector.connect(**db_config)
        return connection
    except Error as e:
        print(f"数据库连接错误: {e}")
        return None
# 用户注册API
@app.route('/api/register', methods=['POST'])
def register():
    try:
        data = request.get_json()

        # 输入验证
        if not data:
            return jsonify({'error': '请提供注册信息'}), 400

        username = data.get('username', '').strip()
        email = data.get('email', '').strip()
        password = data.get('password', '')

        # 验证必填字段
        if not username or not email or not password:
            return jsonify({'error': '用户名、邮箱和密码都是必填项'}), 400

        # 验证格式
        if not validate_username(username):
            return jsonify({'error': '用户名格式不正确（3-20位字母、数字或下划线）'}), 400

        if not validate_email(email):
            return jsonify({'error': '邮箱格式不正确'}), 400

        if not validate_password(password):
            return jsonify({'error': '密码至少8位，必须包含字母和数字'}), 400

        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor()

        # 检查用户名和邮箱是否已存在
        check_query = "SELECT id FROM user WHERE username = %s OR email = %s"
        cursor.execute(check_query, (username, email))
        existing_user = cursor.fetchone()

        if existing_user:
            return jsonify({'error': '用户名或邮箱已存在'}), 409

        # 密码哈希
        password_hash = generate_password_hash(password)

        # 插入新用户
        insert_query = """
            INSERT INTO user (username, email, password_hash, created_at)
            VALUES (%s, %s, %s, %s)
        """
        cursor.execute(insert_query, (username, email, password_hash, datetime.datetime.now()))

        user_id = cursor.lastrowid

        # 生成JWT token
        token = jwt.encode({
            'user_id': user_id,
            'username': username,
            'exp': datetime.datetime.utcnow() + app.config['JWT_EXPIRATION_DELTA']
        }, app.config['SECRET_KEY'], algorithm='HS256')

        return jsonify({
            'message': '注册成功',
            'token': token,
            'user': {
                'id': user_id,
                'username': username,
                'email': email
            }
        }), 201

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 用户登录API
@app.route('/api/login', methods=['POST'])
def login():
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': '请提供登录信息'}), 400

        login_field = data.get('login', '').strip()  # 可以是用户名或邮箱
        password = data.get('password', '')

        if not login_field or not password:
            return jsonify({'error': '请输入用户名/邮箱和密码'}), 400

        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)

        # 查询用户（支持用户名或邮箱登录）
        query = """
            SELECT id, username, email, password_hash
            FROM user
            WHERE username = %s OR email = %s
        """
        cursor.execute(query, (login_field, login_field))
        user = cursor.fetchone()

        if not user or not check_password_hash(user['password_hash'], password):
            return jsonify({'error': '用户名/邮箱或密码错误'}), 401

        # 生成JWT token
        token = jwt.encode({
            'user_id': user['id'],
            'username': user['username'],
            'exp': datetime.datetime.utcnow() + app.config['JWT_EXPIRATION_DELTA']
        }, app.config['SECRET_KEY'], algorithm='HS256')

        return jsonify({
            'message': '登录成功',
            'token': token,
            'user': {
                'id': user['id'],
                'username': user['username'],
                'email': user['email']
            }
        }), 200

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 获取用户信息API（需要认证）
@app.route('/api/user/profile', methods=['GET'])
@token_required
def get_user_profile(current_user_id):
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = connection.cursor(dictionary=True)
        query = "SELECT id, username, email, created_at FROM user WHERE id = %s"
        cursor.execute(query, (current_user_id,))
        user = cursor.fetchone()

        if not user:
            return jsonify({'error': '用户不存在'}), 404

        return jsonify({'user': user}), 200

    except Error as e:
        return jsonify({'error': f'数据库错误: {str(e)}'}), 500
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

# 验证token API
@app.route('/api/verify-token', methods=['POST'])
@token_required
def verify_token(current_user_id):
    return jsonify({'valid': True, 'user_id': current_user_id}), 200

if __name__ == '__main__':
    # 生产环境中应该设置 debug=False
    app.run(debug=os.environ.get('FLASK_ENV') == 'development',
            host='0.0.0.0',
            port=int(os.environ.get('PORT', 5000)))
<template>
  <div class="body">
    <div class="main-box">
      <!-- 消息提示区域 -->
      <div v-if="errorMessage || successMessage" class="message-container">
        <div v-if="errorMessage" class="error-message">
          {{ errorMessage }}
        </div>
        <div v-if="successMessage" class="success-message">
          {{ successMessage }}
        </div>
      </div>

      <!-- 注册表单 -->
      <div :class="['container', 'container-register', { 'is-txl': isLogin }]">
        <form @submit.prevent="register">
          <h2 class="title">注册</h2>
          <div class="form__icons">
            <img class="form__icon" src="../assets/images/wechat.png" alt="微信登录">
            <img class="form__icon" src="../assets/images/alipay.png" alt="支付宝登录">
            <img class="form__icon" src="../assets/images/QQ.png" alt="QQ登录">
          </div>
          <span class="text">或使用邮箱进行注册</span>
          <input
            v-model="registerForm.username"
            class="form__input"
            type="text"
            placeholder="请输入用户名（3-20位字母、数字或下划线）"
            :disabled="loading"
            required
          />
          <input
            v-model="registerForm.email"
            class="form__input"
            type="email"
            placeholder="请输入邮箱"
            :disabled="loading"
            required
          />
          <input
            v-model="registerForm.password"
            class="form__input"
            type="password"
            placeholder="请输入密码（至少8位，包含字母和数字）"
            :disabled="loading"
            required
          />
          <input
            v-model="registerForm.confirmPassword"
            class="form__input"
            type="password"
            placeholder="请确认密码"
            :disabled="loading"
            required
          />
          <button
            type="submit"
            class="form__button"
            :disabled="loading"
            :class="{ 'loading': loading }"
          >
            {{ loading ? '注册中...' : '注册' }}
          </button>
        </form>
      </div>
      <!-- 登录表单 -->
      <div :class="['container', 'container-login', { 'is-txl is-z200': isLogin }]">
        <form @submit.prevent="login">
          <h1 class="title">登录</h1>
          <div class="form__icons">
            <img class="form__icon" src="../assets/images/wechat.png" alt="微信登录">
            <img class="form__icon" src="../assets/images/alipay.png" alt="支付宝登录">
            <img class="form__icon" src="../assets/images/QQ.png" alt="QQ登录">
          </div>
          <span class="text">或使用用户名登录</span>
          <input
            v-model="loginForm.login"
            class="form__input"
            type="text"
            placeholder="用户名/邮箱"
            :disabled="loading"
            required
          />
          <input
            v-model="loginForm.password"
            class="form__input"
            type="password"
            placeholder="请输入密码"
            :disabled="loading"
            required
          />
          <button
            type="submit"
            class="form__button"
            :disabled="loading"
            :class="{ 'loading': loading }"
          >
            {{ loading ? '登录中...' : '登录' }}
          </button>
        </form>
      </div>
      <div :class="['switch', { 'login': isLogin }]">
        <div class="switch__circle"></div>
        <div class="switch__circle switch__circle_top"></div>
        <div class="switch__container">
          <h2>{{ isLogin ? '您好 !' : '欢迎回来 !' }}</h2>
          <p>
            {{
              isLogin
                ? '如果您还没有账号，请点击下方立即注册按钮进行账号注册'
                : '如果您已经注册过账号，请点击下方立即登录按钮进行登录'
            }}
          </p>
          <div class="form__button" @click="toggleMode" :disabled="loading">
            {{ isLogin ? '立即注册' : '立即登录' }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { authAPI, authUtils } from '../utils/api';

// 路由
const router = useRouter();

// 响应式数据
const isLogin = ref(true);
const loading = ref(false);
const errorMessage = ref('');
const successMessage = ref('');

// 表单数据
const loginForm = reactive({
  login: '', // 用户名或邮箱
  password: '',
});

const registerForm = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
});

// 检查是否已登录，如果已登录则跳转到首页
if (authUtils.isAuthenticated()) {
  router.push('/home');
}

// 工具函数
const clearMessages = () => {
  errorMessage.value = '';
  successMessage.value = '';
};

const showError = (message) => {
  errorMessage.value = message;
  successMessage.value = '';
};

const showSuccess = (message) => {
  successMessage.value = message;
  errorMessage.value = '';
};

// 客户端验证函数
const validateEmail = (email) => {
  const pattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return pattern.test(email);
};

const validatePassword = (password) => {
  return password.length >= 8 && /[A-Za-z]/.test(password) && /\d/.test(password);
};

const validateUsername = (username) => {
  const pattern = /^[a-zA-Z0-9_]{3,20}$/;
  return pattern.test(username);
};

// 登录方法
const login = async () => {
  clearMessages();

  // 客户端验证
  if (!loginForm.login.trim() || !loginForm.password) {
    showError('请输入用户名/邮箱和密码');
    return;
  }

  loading.value = true;

  try {
    const response = await authAPI.login({
      login: loginForm.login.trim(),
      password: loginForm.password
    });

    const { token, user, message } = response.data;

    // 使用工具函数存储认证信息
    authUtils.setAuth(token, user);

    showSuccess(message);

    // 延迟跳转，让用户看到成功消息
    setTimeout(() => {
      router.push('/home');
    }, 1000);

  } catch (error) {
    const errorMsg = error.response?.data?.error || '登录失败，请重试';
    showError(errorMsg);
  } finally {
    loading.value = false;
  }
};

// 注册方法
const register = async () => {
  clearMessages();

  // 客户端验证
  if (!registerForm.username.trim() || !registerForm.email.trim() || !registerForm.password || !registerForm.confirmPassword) {
    showError('请填写所有必填字段');
    return;
  }

  if (!validateUsername(registerForm.username)) {
    showError('用户名格式不正确（3-20位字母、数字或下划线）');
    return;
  }

  if (!validateEmail(registerForm.email)) {
    showError('邮箱格式不正确');
    return;
  }

  if (!validatePassword(registerForm.password)) {
    showError('密码至少8位，必须包含字母和数字');
    return;
  }

  if (registerForm.password !== registerForm.confirmPassword) {
    showError('两次输入的密码不一致');
    return;
  }

  loading.value = true;

  try {
    const response = await authAPI.register({
      username: registerForm.username.trim(),
      email: registerForm.email.trim(),
      password: registerForm.password
    });

    const { token, user, message } = response.data;

    // 使用工具函数存储认证信息
    authUtils.setAuth(token, user);

    showSuccess(message);

    // 清空表单
    Object.keys(registerForm).forEach(key => {
      registerForm[key] = '';
    });

    // 延迟跳转
    setTimeout(() => {
      router.push('/home');
    }, 1000);

  } catch (error) {
    const errorMsg = error.response?.data?.error || '注册失败，请重试';
    showError(errorMsg);
  } finally {
    loading.value = false;
  }
};

// 切换登录/注册模式
const toggleMode = () => {
  isLogin.value = !isLogin.value;
  clearMessages();

  // 清空表单
  Object.keys(loginForm).forEach(key => {
    loginForm[key] = '';
  });
  Object.keys(registerForm).forEach(key => {
    registerForm[key] = '';
  });
};
</script>

<style lang="scss" scoped>
.body {
  width: 100%;
  height: 92vh;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: "Montserrat", sans-serif;
  font-size: 12px;
  background-size: cover;
  color: #a0a5a8;
}

/* 消息提示样式 */
.message-container {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  min-width: 300px;
  max-width: 500px;
}

.error-message {
  background-color: #fee;
  color: #c33;
  padding: 12px 20px;
  border-radius: 8px;
  border: 1px solid #fcc;
  margin-bottom: 10px;
  box-shadow: 0 2px 8px rgba(204, 51, 51, 0.2);
  animation: slideDown 0.3s ease-out;
}

.success-message {
  background-color: #efe;
  color: #363;
  padding: 12px 20px;
  border-radius: 8px;
  border: 1px solid #cfc;
  margin-bottom: 10px;
  box-shadow: 0 2px 8px rgba(51, 102, 51, 0.2);
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.main-box {
  position: relative;
  width: 1500px;
  min-width: 1000px;
  min-height: 600px;
  height: 900px;
  padding: 25px;
  background-color: #ecf0f3;
  box-shadow: 1px 1px 100px 10PX #ecf0f3;
  border-radius: 12px;
  overflow: hidden;

  .container {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 0;
    width: 600px;
    height: 100%;
    padding: 25px;
    background-color: #ecf0f3;
    transition: all 1.25s;

    form {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      width: 100%;
      height: 100%;
      color: #a0a5a8;

      .form__icon {
        object-fit: contain;
        width: 50px;
        margin: 0 5px;
        opacity: .5;
        transition: .15s;

        &:hover {
          opacity: 1;
          transition: .15s;
          cursor: pointer;

        }
      }

      .title {
        font-size: 40px;
        font-weight: 700;
        line-height: 3;
        color: #181818;
      }

      .text {
        margin-top: 30px;
        margin-bottom: 12px;
        font-size: 22px;
      }

      .form__input {
        width: 350px;
        height: 40px;
        margin: 4px 0;
        padding-left: 25px;
        font-size: 22px;
        letter-spacing: 0.15px;
        border: none;
        outline: none;
        // font-family: 'Montserrat', sans-serif;
        background-color: #ecf0f3;
        transition: 0.25s ease;
        border-radius: 8px;
        box-shadow: inset 2px 2px 4px #d1d9e6, inset -2px -2px 4px #f9f9f9;

        &::placeholder {
          color: #a0a5a8;
        }
      }
    }
  }

  .container-register {
    z-index: 100;
    left: calc(100% - 600px);
  }

  .container-login {
    left: calc(100% - 600px);
    z-index: 0;
  }

  .is-txl {
    left: 0;
    transition: 1.25s;
    transform-origin: right;
  }

  .is-z200 {
    z-index: 200;
    transition: 1.25s;
  }

  .switch {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 500px;
    padding: 50px;
    z-index: 200;
    transition: 1.25s;
    background-color: #ecf0f3;
    overflow: hidden;
    box-shadow: 4px 4px 10px #d1d9e6, -4px -4px 10px #f9f9f9;
    color: #a0a5a8;

    .switch__circle {
      position: absolute;
      width: 520px;
      height: 500px;
      border-radius: 50%;
      background-color: #ecf0f3;
      box-shadow: inset 8px 8px 12px #d1d9e6, inset -8px -8px 12px #f9f9f9;
      bottom: -60%;
      left: -60%;
      transition: 1.25s;
    }

    .switch__circle_top {
      top: -30%;
      left: 60%;
      width: 300px;
      height: 300px;
    }

    .switch__container {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      position: absolute;
      width: 400px;
      padding: 50px 55px;
      transition: 1.25s;

      h2 {
        font-size: 34px;
        font-weight: 700;
        line-height: 3;
        color: #181818;
      }

      p {
        font-size: 22px;
        letter-spacing: 0.25px;
        text-align: center;
        line-height: 1.6;
      }
    }
  }

  .login {
    left: calc(100% - 500px);

    .switch__circle {
      left: 0;
    }
  }

  .form__button {
    width: 180px;
    height: 50px;
    border-radius: 25px;
    margin-top: 50px;
    text-align: center;
    line-height: 50px;
    font-size: 22px;
    letter-spacing: 2px;
    background-color: #4b70e2;
    color: #f9f9f9;
    cursor: pointer;
    box-shadow: 8px 8px 16px #d1d9e6, -8px -8px 16px #f9f9f9;
    border: none;
    outline: none;
    transition: all 0.3s ease;

    &:hover:not(:disabled) {
      box-shadow: 2px 2px 3px 0 rgba(255, 255, 255, 50%),
        -2px -2px 3px 0 rgba(116, 125, 136, 50%),
        inset -2px -2px 3px 0 rgba(255, 255, 255, 20%),
        inset 2px 2px 3px 0 rgba(0, 0, 0, 30%);
      transform: translateY(-1px);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      background-color: #999;
    }

    &.loading {
      position: relative;

      &::after {
        content: '';
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        width: 16px;
        height: 16px;
        border: 2px solid transparent;
        border-top: 2px solid #fff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }
  }

  @keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
  }

  .form__input:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}
</style>

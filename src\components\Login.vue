<template>
  <div class="body">
    <div class="main-box">
      <div :class="['container', 'container-register', { 'is-txl': isLogin }]">
        <form  @submit.prevent="login">
          <h2 class="title">注册</h2>
          <div class="form__icons">
            <img class="form__icon" src="../assets/images/wechat.png" alt="微信登录">
            <img class="form__icon" src="../assets/images/alipay.png" alt="支付宝登录">
            <img class="form__icon" src="../assets/images/QQ.png" alt="QQ登录">
          </div>
          <span class="text">或使用邮箱进行注册</span>
          <input class="form__input" type="text" placeholder="请输入用户名" />
          <input class="form__input" type="text" placeholder="请输入邮箱" />
          <input class="form__input" type="password" placeholder="请输入密码" />
          <input class="form__input" type="password" placeholder="请输入密码" />
          <div class="form__button">立即注册</div>
        </form>
      </div>
      <div :class="['container', 'container-login', { 'is-txl is-z200': isLogin }]">
        <form>
          <h1 class="title">登录</h1>
          <div class="form__icons">
            <img class="form__icon" src="../assets/images/wechat.png" alt="微信登录">
            <img class="form__icon" src="../assets/images/alipay.png" alt="支付宝登录">
            <img class="form__icon" src="../assets/images/QQ.png" alt="QQ登录">
          </div>
          <span class="text">或使用用户名登录</span>
          <input class="form__input" type="text" v-model="loginForm.email" id="email" required placeholder="用户名/邮箱/手机号" />
          <input class="form__input" v-model="loginForm.password" type="password" id="password"  required placeholder="请输入密码" />
          <div class="form__button">立即登录</div>
        </form>
      </div>
      <div :class="['switch', { 'login': isLogin }]">
        <div class="switch__circle"></div>
        <div class="switch__circle switch__circle_top"></div>
        <div class="switch__container">
          <h2>{{ isLogin ? '您好 !' : '欢迎回来 !' }}</h2>
          <p>
            {{
              isLogin
                ? '如果您还没有账号，请点击下方立即注册按钮进行账号注册'
                : '如果您已经注册过账号，请点击下方立即登录按钮进行登录'
            }}
          </p>
          <div class="form__button" @click="isLogin = !isLogin">
            {{ isLogin ? '立即注册' : '立即登录' }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import axios from 'axios';
import { useRouter } from 'vue-router';

// 路由
const router = useRouter();

// 响应式数据
const isLogin = ref(true);
const loading = ref(false);
const errorMessage = ref('');
const successMessage = ref('');

// 表单数据
const loginForm = reactive({
  login: '', // 用户名或邮箱
  password: '',
});

const registerForm = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
});

// API基础URL
const API_BASE_URL = 'http://127.0.0.1:5000/api';

// 工具函数
const clearMessages = () => {
  errorMessage.value = '';
  successMessage.value = '';
};

const showError = (message) => {
  errorMessage.value = message;
  successMessage.value = '';
};

const showSuccess = (message) => {
  successMessage.value = message;
  errorMessage.value = '';
};

// 客户端验证函数
const validateEmail = (email) => {
  const pattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return pattern.test(email);
};

const validatePassword = (password) => {
  return password.length >= 8 && /[A-Za-z]/.test(password) && /\d/.test(password);
};

const validateUsername = (username) => {
  const pattern = /^[a-zA-Z0-9_]{3,20}$/;
  return pattern.test(username);
};

// 登录方法
const login = async () => {
  clearMessages();

  // 客户端验证
  if (!loginForm.login.trim() || !loginForm.password) {
    showError('请输入用户名/邮箱和密码');
    return;
  }

  loading.value = true;

  try {
    const response = await axios.post(`${API_BASE_URL}/login`, {
      login: loginForm.login.trim(),
      password: loginForm.password
    });

    const { token, user, message } = response.data;

    // 存储token到localStorage
    localStorage.setItem('authToken', token);
    localStorage.setItem('user', JSON.stringify(user));

    showSuccess(message);

    // 延迟跳转，让用户看到成功消息
    setTimeout(() => {
      router.push('/home');
    }, 1000);

  } catch (error) {
    const errorMsg = error.response?.data?.error || '登录失败，请重试';
    showError(errorMsg);
  } finally {
    loading.value = false;
  }
};

// 注册方法
const register = async () => {
  clearMessages();

  // 客户端验证
  if (!registerForm.username.trim() || !registerForm.email.trim() || !registerForm.password || !registerForm.confirmPassword) {
    showError('请填写所有必填字段');
    return;
  }

  if (!validateUsername(registerForm.username)) {
    showError('用户名格式不正确（3-20位字母、数字或下划线）');
    return;
  }

  if (!validateEmail(registerForm.email)) {
    showError('邮箱格式不正确');
    return;
  }

  if (!validatePassword(registerForm.password)) {
    showError('密码至少8位，必须包含字母和数字');
    return;
  }

  if (registerForm.password !== registerForm.confirmPassword) {
    showError('两次输入的密码不一致');
    return;
  }

  loading.value = true;

  try {
    const response = await axios.post(`${API_BASE_URL}/register`, {
      username: registerForm.username.trim(),
      email: registerForm.email.trim(),
      password: registerForm.password
    });

    const { token, user, message } = response.data;

    // 存储token到localStorage
    localStorage.setItem('authToken', token);
    localStorage.setItem('user', JSON.stringify(user));

    showSuccess(message);

    // 清空表单
    Object.keys(registerForm).forEach(key => {
      registerForm[key] = '';
    });

    // 延迟跳转
    setTimeout(() => {
      router.push('/home');
    }, 1000);

  } catch (error) {
    const errorMsg = error.response?.data?.error || '注册失败，请重试';
    showError(errorMsg);
  } finally {
    loading.value = false;
  }
};

// 切换登录/注册模式
const toggleMode = () => {
  isLogin.value = !isLogin.value;
  clearMessages();

  // 清空表单
  Object.keys(loginForm).forEach(key => {
    loginForm[key] = '';
  });
  Object.keys(registerForm).forEach(key => {
    registerForm[key] = '';
  });
};
</script>

<style lang="scss" scoped>
.body {
  width: 100%;
  height: 92vh;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: "Montserrat", sans-serif;
  font-size: 12px;
  background-size: cover;
  color: #a0a5a8;

}

.main-box {
  position: relative;
  width: 1500px;
  min-width: 1000px;
  min-height: 600px;
  height: 900px;
  padding: 25px;
  background-color: #ecf0f3;
  box-shadow: 1px 1px 100px 10PX #ecf0f3;
  border-radius: 12px;
  overflow: hidden;

  .container {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 0;
    width: 600px;
    height: 100%;
    padding: 25px;
    background-color: #ecf0f3;
    transition: all 1.25s;

    form {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      width: 100%;
      height: 100%;
      color: #a0a5a8;

      .form__icon {
        object-fit: contain;
        width: 50px;
        margin: 0 5px;
        opacity: .5;
        transition: .15s;

        &:hover {
          opacity: 1;
          transition: .15s;
          cursor: pointer;

        }
      }

      .title {
        font-size: 40px;
        font-weight: 700;
        line-height: 3;
        color: #181818;
      }

      .text {
        margin-top: 30px;
        margin-bottom: 12px;
        font-size: 22px;
      }

      .form__input {
        width: 350px;
        height: 40px;
        margin: 4px 0;
        padding-left: 25px;
        font-size: 22px;
        letter-spacing: 0.15px;
        border: none;
        outline: none;
        // font-family: 'Montserrat', sans-serif;
        background-color: #ecf0f3;
        transition: 0.25s ease;
        border-radius: 8px;
        box-shadow: inset 2px 2px 4px #d1d9e6, inset -2px -2px 4px #f9f9f9;

        &::placeholder {
          color: #a0a5a8;
        }
      }
    }
  }

  .container-register {
    z-index: 100;
    left: calc(100% - 600px);
  }

  .container-login {
    left: calc(100% - 600px);
    z-index: 0;
  }

  .is-txl {
    left: 0;
    transition: 1.25s;
    transform-origin: right;
  }

  .is-z200 {
    z-index: 200;
    transition: 1.25s;
  }

  .switch {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 500px;
    padding: 50px;
    z-index: 200;
    transition: 1.25s;
    background-color: #ecf0f3;
    overflow: hidden;
    box-shadow: 4px 4px 10px #d1d9e6, -4px -4px 10px #f9f9f9;
    color: #a0a5a8;

    .switch__circle {
      position: absolute;
      width: 520px;
      height: 500px;
      border-radius: 50%;
      background-color: #ecf0f3;
      box-shadow: inset 8px 8px 12px #d1d9e6, inset -8px -8px 12px #f9f9f9;
      bottom: -60%;
      left: -60%;
      transition: 1.25s;
    }

    .switch__circle_top {
      top: -30%;
      left: 60%;
      width: 300px;
      height: 300px;
    }

    .switch__container {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      position: absolute;
      width: 400px;
      padding: 50px 55px;
      transition: 1.25s;

      h2 {
        font-size: 34px;
        font-weight: 700;
        line-height: 3;
        color: #181818;
      }

      p {
        font-size: 22px;
        letter-spacing: 0.25px;
        text-align: center;
        line-height: 1.6;
      }
    }
  }

  .login {
    left: calc(100% - 600px);

    .switch__circle {
      left: 0;
    }
  }

  .form__button {
    width: 180px;
    height: 50px;
    border-radius: 25px;
    margin-top: 50px;
    text-align: center;
    line-height: 50px;
    font-size: 22px;
    letter-spacing: 2px;
    background-color: #4b70e2;
    color: #f9f9f9;
    cursor: pointer;
    box-shadow: 8px 8px 16px #d1d9e6, -8px -8px 16px #f9f9f9;

    &:hover {
      box-shadow: 2px 2px 3px 0 rgba(255, 255, 255, 50%),
        -2px -2px 3px 0 rgba(116, 125, 136, 50%),
        inset -2px -2px 3px 0 rgba(255, 255, 255, 20%),
        inset 2px 2px 3px 0 rgba(0, 0, 0, 30%);
    }
  }
}
</style>

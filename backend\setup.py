#!/usr/bin/env python3
"""
安全登录系统设置脚本
用于初始化数据库和创建环境配置
"""

import mysql.connector
from mysql.connector import Error
import os
import secrets
import getpass

def generate_secret_key():
    """生成安全的密钥"""
    return secrets.token_urlsafe(32)

def create_env_file():
    """创建.env文件"""
    print("=== 创建环境配置文件 ===")
    
    # 数据库配置
    db_host = input("数据库主机 (默认: localhost): ").strip() or "localhost"
    db_user = input("数据库用户名 (默认: root): ").strip() or "root"
    db_password = getpass.getpass("数据库密码: ")
    db_name = input("数据库名称 (默认: security): ").strip() or "security"
    
    # 生成密钥
    secret_key = generate_secret_key()
    
    # Flask环境
    flask_env = input("Flask环境 (development/production, 默认: development): ").strip() or "development"
    
    # 端口
    port = input("服务器端口 (默认: 5000): ").strip() or "5000"
    
    env_content = f"""# 数据库配置
DB_HOST={db_host}
DB_USER={db_user}
DB_PASSWORD={db_password}
DB_NAME={db_name}

# Flask配置
SECRET_KEY={secret_key}
FLASK_ENV={flask_env}

# 服务器配置
PORT={port}

# JWT配置
JWT_EXPIRATION_HOURS=24
"""
    
    with open('.env', 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    print("✅ .env 文件创建成功!")
    return {
        'host': db_host,
        'user': db_user,
        'password': db_password,
        'database': db_name
    }

def init_database(db_config):
    """初始化数据库"""
    print("\n=== 初始化数据库 ===")
    
    try:
        # 连接MySQL服务器（不指定数据库）
        connection = mysql.connector.connect(
            host=db_config['host'],
            user=db_config['user'],
            password=db_config['password']
        )
        
        cursor = connection.cursor()
        
        # 创建数据库
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS {db_config['database']} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print(f"✅ 数据库 '{db_config['database']}' 创建成功!")
        
        # 使用数据库
        cursor.execute(f"USE {db_config['database']}")
        
        # 读取并执行SQL文件
        with open('init_db.sql', 'r', encoding='utf-8') as f:
            sql_commands = f.read().split(';')
        
        for command in sql_commands:
            command = command.strip()
            if command:
                cursor.execute(command)
        
        print("✅ 数据库表创建成功!")
        
        # 创建测试用户（可选）
        create_test_user = input("\n是否创建测试用户? (y/N): ").strip().lower()
        if create_test_user == 'y':
            from werkzeug.security import generate_password_hash
            import datetime
            
            test_username = input("测试用户名 (默认: admin): ").strip() or "admin"
            test_email = input("测试邮箱 (默认: <EMAIL>): ").strip() or "<EMAIL>"
            test_password = getpass.getpass("测试密码: ")
            
            password_hash = generate_password_hash(test_password)
            
            insert_query = """
                INSERT INTO user (username, email, password_hash, created_at) 
                VALUES (%s, %s, %s, %s)
            """
            cursor.execute(insert_query, (test_username, test_email, password_hash, datetime.datetime.now()))
            
            print(f"✅ 测试用户 '{test_username}' 创建成功!")
        
    except Error as e:
        print(f"❌ 数据库错误: {e}")
        return False
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()
    
    return True

def install_dependencies():
    """安装Python依赖"""
    print("\n=== 安装Python依赖 ===")
    os.system("pip install -r requirements.txt")
    print("✅ Python依赖安装完成!")

def main():
    print("🔐 安全登录系统设置向导")
    print("=" * 40)
    
    # 检查必要文件
    if not os.path.exists('init_db.sql'):
        print("❌ 找不到 init_db.sql 文件!")
        return
    
    if not os.path.exists('requirements.txt'):
        print("❌ 找不到 requirements.txt 文件!")
        return
    
    try:
        # 1. 安装依赖
        install_dependencies()
        
        # 2. 创建环境配置
        db_config = create_env_file()
        
        # 3. 初始化数据库
        if init_database(db_config):
            print("\n🎉 设置完成!")
            print("\n下一步:")
            print("1. 运行后端服务: python conn.py")
            print("2. 在另一个终端运行前端: npm run dev")
            print("3. 访问 http://localhost:5173")
        else:
            print("\n❌ 数据库初始化失败，请检查配置!")
    
    except KeyboardInterrupt:
        print("\n\n⚠️  设置被用户取消")
    except Exception as e:
        print(f"\n❌ 设置过程中出现错误: {e}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
数据库连接测试脚本
用于诊断登录问题
"""

import mysql.connector
from mysql.connector import Error
from dotenv import load_dotenv
import os
from werkzeug.security import generate_password_hash

# 加载环境变量
load_dotenv()

# 数据库连接配置
db_config = {
    'host': os.environ.get('DB_HOST', 'localhost'),
    'user': os.environ.get('DB_USER', 'root'),
    'password': os.environ.get('DB_PASSWORD', '123456'),
    'database': os.environ.get('DB_NAME', 'security'),
    'autocommit': True
}

def test_connection():
    """测试数据库连接"""
    print("🔍 测试数据库连接...")
    print(f"配置: {db_config}")
    
    try:
        connection = mysql.connector.connect(**db_config)
        print("✅ 数据库连接成功!")
        return connection
    except Error as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def check_database_exists(connection):
    """检查数据库是否存在"""
    print("\n🔍 检查数据库...")
    try:
        cursor = connection.cursor()
        cursor.execute("SHOW DATABASES")
        databases = [db[0] for db in cursor.fetchall()]
        
        if db_config['database'] in databases:
            print(f"✅ 数据库 '{db_config['database']}' 存在")
            return True
        else:
            print(f"❌ 数据库 '{db_config['database']}' 不存在")
            print(f"可用数据库: {databases}")
            return False
    except Error as e:
        print(f"❌ 检查数据库失败: {e}")
        return False

def check_tables(connection):
    """检查表是否存在"""
    print("\n🔍 检查数据表...")
    try:
        cursor = connection.cursor()
        cursor.execute("SHOW TABLES")
        tables = [table[0] for table in cursor.fetchall()]
        
        if 'user' in tables:
            print("✅ user表存在")
            return True
        else:
            print("❌ user表不存在")
            print(f"可用表: {tables}")
            return False
    except Error as e:
        print(f"❌ 检查表失败: {e}")
        return False

def check_users(connection):
    """检查用户数据"""
    print("\n🔍 检查用户数据...")
    try:
        cursor = connection.cursor(dictionary=True)
        cursor.execute("SELECT id, username, email FROM user LIMIT 5")
        users = cursor.fetchall()
        
        if users:
            print(f"✅ 找到 {len(users)} 个用户:")
            for user in users:
                print(f"  - ID: {user['id']}, 用户名: {user['username']}, 邮箱: {user['email']}")
            return True
        else:
            print("⚠️  没有找到用户数据")
            return False
    except Error as e:
        print(f"❌ 检查用户失败: {e}")
        return False

def create_test_user(connection):
    """创建测试用户"""
    print("\n🔧 创建测试用户...")
    try:
        cursor = connection.cursor()
        
        # 检查测试用户是否已存在
        cursor.execute("SELECT id FROM user WHERE username = 'testuser'")
        if cursor.fetchone():
            print("⚠️  测试用户已存在")
            return True
        
        # 创建测试用户
        password_hash = generate_password_hash('password123')
        insert_query = """
            INSERT INTO user (username, email, password_hash) 
            VALUES (%s, %s, %s)
        """
        cursor.execute(insert_query, ('testuser', '<EMAIL>', password_hash))
        
        print("✅ 测试用户创建成功!")
        print("   用户名: testuser")
        print("   邮箱: <EMAIL>")
        print("   密码: password123")
        return True
        
    except Error as e:
        print(f"❌ 创建测试用户失败: {e}")
        return False

def main():
    print("🔐 数据库诊断工具")
    print("=" * 40)
    
    # 1. 测试连接
    connection = test_connection()
    if not connection:
        print("\n💡 解决方案:")
        print("1. 检查MySQL服务是否启动")
        print("2. 检查数据库配置信息")
        print("3. 检查用户权限")
        return
    
    # 2. 检查数据库
    if not check_database_exists(connection):
        print("\n💡 解决方案:")
        print("1. 运行 python setup.py 创建数据库")
        print("2. 手动创建数据库: CREATE DATABASE security;")
        return
    
    # 3. 检查表
    if not check_tables(connection):
        print("\n💡 解决方案:")
        print("1. 运行 python setup.py 创建表")
        print("2. 手动执行 init_db.sql")
        return
    
    # 4. 检查用户
    has_users = check_users(connection)
    
    if not has_users:
        create_user = input("\n是否创建测试用户? (y/N): ").strip().lower()
        if create_user == 'y':
            create_test_user(connection)
    
    print("\n🎉 数据库检查完成!")
    print("\n下一步:")
    print("1. 启动后端服务: python conn.py")
    print("2. 使用测试用户登录")
    
    if connection:
        connection.close()

if __name__ == "__main__":
    main()
